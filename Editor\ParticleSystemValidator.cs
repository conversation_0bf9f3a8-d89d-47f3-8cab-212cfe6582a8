#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;

public class ParticleSystemValidator : OdinEditorWindow
{
    [MenuItem("自定义工具/资源检查/ParticleSystem异常检测")]
    static void Init()
    {
        GetWindow<ParticleSystemValidator>("ParticleSystem异常检测").Show();
    }

    [System.Serializable]
    public class ParticleSystemIssue
    {
        public string issueName;
        public string issueType;
        public GameObject prefab;
        public GameObject particleObject;
        public string description;
        public ParticleSystem particleSystem;
    }

    [Title("检测范围配置")]
    [LabelText("检测文件夹路径")]
    [FolderPath]
    public string[] targetFolders = 
    {
        "Assets/Game/Effects",
        "Assets/Game/Model",
        "Assets/Game/Actors",
        "Assets/Game/Environments",
        "Assets/Game/CG",
    };

    [LabelText("单个预制体检测")]
    [AssetsOnly]
    public GameObject[] individualPrefabs = new GameObject[0];

    [Title("检测类型配置")]
    
    [FoldoutGroup("异常检测")]
    [LabelText("检测SubEmitter异常")]
    public bool checkSubEmitters = true;

    [FoldoutGroup("异常检测")]
    [LabelText("检测Mesh丢失")]
    public bool checkMissingMesh = true;

    [FoldoutGroup("异常检测")]
    [LabelText("检测材质丢失")]
    public bool checkMissingMaterials = true;

    [FoldoutGroup("异常检测")]
    [LabelText("检测贴图丢失")]
    public bool checkMissingTextures = false;

    [FoldoutGroup("异常检测")]
    [LabelText("检测渲染队列异常")]
    public bool checkRenderQueue = true;

    [FoldoutGroup("异常检测")]
    [LabelText("检测循环播放但Duration为0")]
    public bool checkLoopingWithZeroDuration = true;

    [FoldoutGroup("异常检测")]
    [LabelText("检测Shape Arc为0")]
    public bool checkShapeArcZero = true;

    [FoldoutGroup("性能检测")]
    [LabelText("检测过大的MaxParticles")]
    public bool checkMaxParticles = false;
    [FoldoutGroup("性能检测")]
    [ShowIf("checkMaxParticles")]
    public int maxParticlesThreshold = 1000;

    [FoldoutGroup("性能检测")]
    [LabelText("检测过长的预热时间")]
    public bool checkPrewarmTime = false;
    [FoldoutGroup("性能检测")]
    [ShowIf("checkPrewarmTime")]
    public float maxPrewarmTimeThreshold = 20f;

    [FoldoutGroup("性能检测")]
    [LabelText("检测过长的生命周期")]
    public bool checkLifetime = false;
    [FoldoutGroup("性能检测")]
    [ShowIf("checkLifetime")]
    public float maxLifetimeThreshold = 30f;

    [FoldoutGroup("性能检测")]
    [LabelText("检测动画曲线过多关键帧")]
    public bool checkAnimationCurves = false;
    [FoldoutGroup("性能检测")]
    [ShowIf("checkAnimationCurves")]
    public int maxKeyframesThreshold = 20;

    [Title("检测结果")]
    [ReadOnly]
    [ListDrawerSettings(Expanded = true)]
    public List<ParticleSystemIssue> detectedIssues = new List<ParticleSystemIssue>();

    [Button("开始检测文件夹", ButtonSizes.Large)]
    [GUIColor(0.4f, 0.8f, 1)]
    public void StartFolderValidation()
    {
        detectedIssues.Clear();
        
        if (targetFolders == null || targetFolders.Length == 0)
        {
            Debug.LogError("请先设置检测文件夹路径！");
            return;
        }

        List<GameObject> prefabs = new List<GameObject>();
        
        foreach (string folder in targetFolders)
        {
            if (!Directory.Exists(folder))
            {
                Debug.LogError($"文件夹不存在: {folder}");
                continue;
            }

            var folderPrefabs = AssetDatabase.FindAssets("t:Prefab", new string[] { folder })
                .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
                .Select(path => AssetDatabase.LoadAssetAtPath<GameObject>(path))
                .Where(obj => obj != null)
                .ToList();
            
            prefabs.AddRange(folderPrefabs);
        }

        ValidatePrefabList(prefabs, "文件夹检测");
    }

    [Button("开始检测单个预制体", ButtonSizes.Large)]
    [GUIColor(0.8f, 1f, 0.4f)]
    public void StartIndividualValidation()
    {
        detectedIssues.Clear();
        
        if (individualPrefabs == null || individualPrefabs.Length == 0)
        {
            Debug.LogError("请先设置要检测的预制体！");
            return;
        }

        List<GameObject> prefabs = individualPrefabs.Where(p => p != null).ToList();
        ValidatePrefabList(prefabs, "单个预制体检测");
    }

    [Button("检测UI预制体违规特效", ButtonSizes.Large)]
    [GUIColor(1f, 0.6f, 0.8f)]
    public void StartUIValidation()
    {
        detectedIssues.Clear();
        
        string uiFolder = "Assets/Game/UIs";
        
        if (!Directory.Exists(uiFolder))
        {
            Debug.LogError($"UI文件夹不存在: {uiFolder}");
            return;
        }

        List<GameObject> prefabs = AssetDatabase.FindAssets("t:Prefab", new string[] { uiFolder })
            .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
            .Select(path => AssetDatabase.LoadAssetAtPath<GameObject>(path))
            .Where(obj => obj != null)
            .ToList();

        ValidateUIPrefabList(prefabs);
    }

    private void ValidateUIPrefabList(List<GameObject> prefabs)
    {
        int successCount = 0;
        int errorCount = 0;
        int issueCount = 0;
        
        for (int i = 0; i < prefabs.Count; i++)
        {
            var prefab = prefabs[i];
            EditorUtility.DisplayProgressBar("UI预制体检测进行中", $"检测: {prefab.name}", i / (float)prefabs.Count);
            
            try
            {
                if (ValidateUIPrefab(prefab))
                {
                    issueCount++;
                }
                successCount++;
            }
            catch (System.Exception ex)
            {
                errorCount++;
                Debug.LogError($"检测UI预制体 '{prefab.name}' 时发生错误: {ex.Message}\n路径: {AssetDatabase.GetAssetPath(prefab)}");
                Debug.LogException(ex);
            }
        }

        EditorUtility.ClearProgressBar();
        
        Debug.Log($"UI预制体检测完成！检测了 {prefabs.Count} 个预制体，成功 {successCount} 个，错误 {errorCount} 个。");
        
        if (issueCount > 0)
        {
            Debug.LogError($"发现 {issueCount} 个UI预制体违规挂载了ParticleSystem组件！");
        }
        else
        {
            Debug.Log("所有UI预制体都符合规范，没有违规挂载ParticleSystem组件。");
        }
        
        if (errorCount > 0)
        {
            Debug.LogError($"有 {errorCount} 个UI预制体检测时出现错误，请查看Console了解详情");
        }
    }

    private bool ValidateUIPrefab(GameObject prefab)
    {
        ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>(true);
        
        if (particleSystems != null && particleSystems.Length > 0)
        {
            AddIssue("UI违规", "UI预制体挂载ParticleSystem", prefab, prefab, null,
                    "UI预制体不应直接挂载ParticleSystem组件");
            return true;
        }
        
        return false;
    }

    private string GetGameObjectPath(Transform target, Transform root)
    {
        if (target == root) return root.name;
        
        List<string> path = new List<string>();
        Transform current = target;
        
        while (current != null && current != root)
        {
            path.Add(current.name);
            current = current.parent;
        }
        
        if (current == root)
        {
            path.Add(root.name);
            path.Reverse();
            return string.Join("/", path);
        }
        
        return target.name;
    }

    [Button("深度SubEmitter异常扫描", ButtonSizes.Large)]
    [GUIColor(1f, 0.4f, 0.8f)]
    public void DeepSubEmitterScan()
    {
        detectedIssues.Clear();
        
        if (targetFolders == null || targetFolders.Length == 0)
        {
            Debug.LogError("请先设置检测文件夹路径！");
            return;
        }

        List<GameObject> prefabs = new List<GameObject>();
        
        foreach (string folder in targetFolders)
        {
            if (!Directory.Exists(folder))
            {
                Debug.LogError($"文件夹不存在: {folder}");
                continue;
            }

            var folderPrefabs = AssetDatabase.FindAssets("t:Prefab", new string[] { folder })
                .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
                .Select(path => AssetDatabase.LoadAssetAtPath<GameObject>(path))
                .Where(obj => obj != null)
                .ToList();
            
            prefabs.AddRange(folderPrefabs);
        }

        int totalPrefabs = prefabs.Count;
        int processedPrefabs = 0;
        int foundIssues = 0;
        
        Debug.LogError($"开始深度SubEmitter扫描，共 {totalPrefabs} 个预制体...");
        
        for (int i = 0; i < prefabs.Count; i++)
        {
            var prefab = prefabs[i];
            EditorUtility.DisplayProgressBar("深度SubEmitter扫描", $"扫描: {prefab.name}", i / (float)prefabs.Count);
            
            try
            {
                int issuesBefore = detectedIssues.Count;
                PerformDeepSubEmitterCheck(prefab);
                int issuesAfter = detectedIssues.Count;
                
                if (issuesAfter > issuesBefore)
                {
                    foundIssues++;
                    Debug.LogWarning($"在预制体 {prefab.name} 中发现 {issuesAfter - issuesBefore} 个SubEmitter问题");
                }
                
                processedPrefabs++;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"深度扫描预制体 '{prefab.name}' 时发生错误: {ex.Message}");
                Debug.LogException(ex);
            }
        }

        EditorUtility.ClearProgressBar();
        
        Debug.LogError($"深度SubEmitter扫描完成！");
        Debug.LogError($"- 扫描预制体数量: {totalPrefabs}");
        Debug.LogError($"- 成功扫描: {processedPrefabs}");
        Debug.LogError($"- 发现问题的预制体: {foundIssues}");
        Debug.LogError($"- 总问题数量: {detectedIssues.Count}");
        
        if (detectedIssues.Count > 0)
        {
            var groupedIssues = detectedIssues.GroupBy(i => i.issueName);
            Debug.LogError("问题类型统计:");
            foreach (var group in groupedIssues)
            {
                Debug.LogError($"- {group.Key}: {group.Count()} 个");
            }
        }
    }

    private void PerformDeepSubEmitterCheck(GameObject prefab)
    {
        ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>(true);
        
        if (particleSystems == null || particleSystems.Length == 0)
            return;

        foreach (var ps in particleSystems)
        {
            if (ps.subEmitters.enabled)
            {
                CheckSubEmittersEnhanced(prefab, ps, new HashSet<ParticleSystem>(), "");
            }
        }
    }

    [Button("修复SubEmitter问题", ButtonSizes.Medium)]
    [GUIColor(1f, 0.8f, 0.4f)]
    public void FixSubEmitterIssues()
    {
        var subEmitterIssues = detectedIssues.Where(i => i.issueType == "SubEmitter异常").ToList();
        if (subEmitterIssues.Count == 0)
        {
            Debug.LogError("没有发现SubEmitter问题需要修复。");
            return;
        }

        int fixedCount = 0;
        foreach (var issue in subEmitterIssues)
        {
            if (FixSubEmitterForPrefab(issue.prefab))
            {
                fixedCount++;
            }
        }

        Debug.LogError($"已修复 {fixedCount} 个预制体的SubEmitter问题。");
        
        // 重新检测以更新结果
        if (targetFolders != null && targetFolders.Length > 0)
        {
            StartFolderValidation();
        }
        else if (individualPrefabs != null && individualPrefabs.Length > 0)
        {
            StartIndividualValidation();
        }
    }

    [Button("修复Shape Arc问题", ButtonSizes.Medium)]
    [GUIColor(0.8f, 1f, 0.4f)]
    public void FixShapeArcIssues()
    {
        var shapeArcIssues = detectedIssues.Where(i => i.issueType == "Shape配置异常" && i.issueName == "Shape Arc为0").ToList();
        if (shapeArcIssues.Count == 0)
        {
            Debug.LogError("没有发现Shape Arc问题需要修复。");
            return;
        }

        int fixedCount = 0;
        foreach (var issue in shapeArcIssues)
        {
            if (FixShapeArcForPrefab(issue.prefab))
            {
                fixedCount++;
            }
        }

        Debug.LogError($"已修复 {fixedCount} 个预制体的Shape Arc问题。");
        
        // 重新检测以更新结果
        if (targetFolders != null && targetFolders.Length > 0)
        {
            StartFolderValidation();
        }
        else if (individualPrefabs != null && individualPrefabs.Length > 0)
        {
            StartIndividualValidation();
        }
    }

    private void ValidatePrefabList(List<GameObject> prefabs, string validationType)
    {
        int successCount = 0;
        int errorCount = 0;
        
        for (int i = 0; i < prefabs.Count; i++)
        {
            var prefab = prefabs[i];
            EditorUtility.DisplayProgressBar($"{validationType}进行中", $"检测: {prefab.name}", i / (float)prefabs.Count);
            
            try
            {
                ValidatePrefab(prefab);
                successCount++;
            }
            catch (System.Exception ex)
            {
                errorCount++;
                Debug.LogError($"检测预制体 '{prefab.name}' 时发生错误: {ex.Message}\n路径: {AssetDatabase.GetAssetPath(prefab)}");
                Debug.LogException(ex);
            }
        }

        EditorUtility.ClearProgressBar();
        
        Debug.Log($"{validationType}完成！检测了 {prefabs.Count} 个预制体，成功 {successCount} 个，错误 {errorCount} 个，发现 {detectedIssues.Count} 个问题。");
        
        if (detectedIssues.Count > 0)
        {
            Debug.LogError($"发现以下问题类型: {string.Join(", ", detectedIssues.Select(i => i.issueType).Distinct())}");
        }
        
        if (errorCount > 0)
        {
            Debug.LogError($"有 {errorCount} 个预制体检测时出现错误，请查看Console了解详情");
        }
    }

    private void ValidatePrefab(GameObject prefab)
    {
        ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>(true);
        
        if (particleSystems == null || particleSystems.Length == 0)
            return;

        foreach (var ps in particleSystems)
        {
            if (checkSubEmitters && ps.subEmitters.enabled)
                CheckSubEmitters(prefab, ps);
            
            if (checkMissingMaterials)
                CheckMissingMaterials(prefab, ps);

            if (checkMissingMesh)
                CheckMissingMesh(prefab, ps);

            if (checkMissingTextures)
                CheckMissingTextures(prefab, ps);

            if (checkRenderQueue)
                CheckRenderQueue(prefab, ps);

            if (checkMaxParticles)
                CheckMaxParticles(prefab, ps);
            
            if (checkPrewarmTime)
                CheckPrewarmTime(prefab, ps);

            if (checkLifetime)
                CheckLifetime(prefab, ps);
            
            if (checkAnimationCurves)
                CheckAnimationCurves(prefab, ps);
            
            if (checkLoopingWithZeroDuration)
                CheckLoopingWithZeroDuration(prefab, ps);
            
            if (checkShapeArcZero)
                CheckShapeArcZero(prefab, ps);
        }
    }

#region 异常检测
#region 检查SubEmitter
    private void CheckSubEmitters(GameObject prefab, ParticleSystem ps)
    {
        CheckSubEmittersEnhanced(prefab, ps, new HashSet<ParticleSystem>(), "");
    }

    private void CheckSubEmittersEnhanced(GameObject prefab, ParticleSystem ps, HashSet<ParticleSystem> visitedSystems, string parentPath)
    {
        var subEmitters = ps.subEmitters;
        List<int> missingIndices = new List<int>();
        List<int> hierarchyErrorIndices = new List<int>();
        List<int> crossPrefabIndices = new List<int>();
        List<int> inactiveIndices = new List<int>();
        List<int> disabledIndices = new List<int>();
        List<int> circularIndices = new List<int>();
        
        string currentPath = string.IsNullOrEmpty(parentPath) ? ps.name : $"{parentPath} -> {ps.name}";
        
        for (int i = 0; i < subEmitters.subEmittersCount; i++)
        {
            ParticleSystem subEmitter = null;
            try
            {
                subEmitter = subEmitters.GetSubEmitterSystem(i);
            }
            catch (System.Exception ex)
            {
                AddIssue("SubEmitter异常", "SubEmitter访问异常", prefab, ps.gameObject, ps,
                    $"访问SubEmitter索引 {i} 时发生异常: {ex.Message}");
                continue;
            }
            
            if (subEmitter == null)
            {
                missingIndices.Add(i);
                continue;
            }

            // 检查循环引用
            if (visitedSystems.Contains(subEmitter))
            {
                circularIndices.Add(i);
                AddIssue("SubEmitter异常", "SubEmitter循环引用", prefab, ps.gameObject, ps,
                    $"SubEmitter索引 {i} 形成循环引用，路径: {currentPath} -> {subEmitter.name}");
                continue;
            }

            // 检查层级关系
            if (!IsValidSubEmitterHierarchy(ps, subEmitter))
            {
                hierarchyErrorIndices.Add(i);
                
                // 进一步诊断层级问题的原因
                string hierarchyDiagnosis = DiagnoseHierarchyIssue(ps, subEmitter);
                AddIssue("SubEmitter异常", "SubEmitter层级关系错误", prefab, ps.gameObject, ps,
                    $"SubEmitter必须是直接子对象（当前父对象:  {subEmitter.transform.parent?.name}");
            }

            // 检查是否跨预制体引用
            if (IsCrossPrefabReference(prefab, subEmitter))
            {
                crossPrefabIndices.Add(i);
                AddIssue("SubEmitter异常", "SubEmitter跨预制体引用", prefab, ps.gameObject, ps,
                    $"SubEmitter索引 {i} ({subEmitter.name}) 引用了其他预制体中的粒子系统");
            }

            // 检查GameObject激活状态
            if (!subEmitter.gameObject.activeSelf)
            {
                inactiveIndices.Add(i);
                AddIssue("SubEmitter异常", "SubEmitter GameObject未激活", prefab, ps.gameObject, ps,
                    $"SubEmitter索引 {i} ({subEmitter.name}) 的GameObject未激活");
            }

            // 检查组件播放状态和发射模块状态
            if (!subEmitter.emission.enabled)
            {
                disabledIndices.Add(i);
                AddIssue("SubEmitter异常", "SubEmitter发射模块未启用", prefab, ps.gameObject, ps,
                    $"SubEmitter索引 {i} ({subEmitter.name}) 的发射模块未启用");
            }

            // 递归检查SubEmitter的SubEmitter（避免循环引用）
            if (!circularIndices.Contains(i) && subEmitter.subEmitters.enabled && subEmitter.subEmitters.subEmittersCount > 0)
            {
                var newVisitedSystems = new HashSet<ParticleSystem>(visitedSystems) { ps };
                CheckSubEmittersEnhanced(prefab, subEmitter, newVisitedSystems, currentPath);
            }
        }

        // 报告各种问题
        if (missingIndices.Count > 0)
        {
            if (missingIndices.Count == subEmitters.subEmittersCount)
            {
                AddIssue("SubEmitter异常", "SubEmitter全部引用丢失", prefab, ps.gameObject, ps, 
                    $"所有SubEmitter引用都为空，共 {subEmitters.subEmittersCount} 个");
            }
            else
            {
                string indices = string.Join(", ", missingIndices);
                AddIssue("SubEmitter异常", "SubEmitter部分引用丢失", prefab, ps.gameObject, ps,
                    $"SubEmitter索引 [{indices}] 的引用为空");
            }
        }

        // 报告深度过深的问题
        int currentDepth = parentPath.Split(new string[] { " -> " }, System.StringSplitOptions.None).Length;
        if (currentDepth > 3) // 设置最大深度阈值为3
        {
            AddIssue("SubEmitter异常", "SubEmitter嵌套过深", prefab, ps.gameObject, ps,
                $"SubEmitter嵌套深度过深 (当前深度: {currentDepth})，路径: {currentPath}");
        }

        // 检查SubEmitter数量是否过多
        // if (subEmitters.subEmittersCount > 5) 
        // {
        //     AddIssue("SubEmitter异常", "SubEmitter数量过多", prefab, ps.gameObject, ps,
        //         $"SubEmitter数量过多 ({subEmitters.subEmittersCount} 个)，可能影响性能");
        // }
    }

    private bool IsValidSubEmitterHierarchy(ParticleSystem parent, ParticleSystem subEmitter)
    {
        if (parent == null || subEmitter == null) return false;
        
        // 检查是否是直接子对象
        return subEmitter.transform.parent == parent.transform;
    }

    private string DiagnoseHierarchyIssue(ParticleSystem parent, ParticleSystem subEmitter)
    {
        if (parent == null || subEmitter == null) 
            return "parent或subEmitter为null";
        
        // 直接子对象检查
        if (subEmitter.transform.parent != parent.transform)
        {
            return $"SubEmitter必须是直接子对象（当前父对象: {subEmitter.transform.parent?.name})";
        }

        // 检查是否在同一个根对象下
        Transform parentRoot = parent.transform.root;
        Transform subEmitterRoot = subEmitter.transform.root;
        
        if (parentRoot != subEmitterRoot)
        {
            return $"不在同一个根对象下 (parent根: {parentRoot.name}, subEmitter根: {subEmitterRoot.name})";
        }
        
        // 检查是否是兄弟关系
        if (parent.transform.parent == subEmitter.transform.parent)
        {
            return "SubEmitter与父粒子系统是兄弟关系，应该是父子关系";
        }
        
        // 检查是否是反向层级（subEmitter是parent的父级）
        if (parent.transform.IsChildOf(subEmitter.transform))
        {
            return "SubEmitter是父粒子系统的父级，层级关系颠倒";
        }
        
        // 计算层级距离
        int distance = GetHierarchyDistance(parent.transform, subEmitter.transform);
        if (distance == -1)
        {
            return "SubEmitter不在父粒子系统的层级树中";
        }
        
        return $"未知的层级问题，距离: {distance}";
    }

    private int GetHierarchyDistance(Transform parent, Transform child)
    {
        if (parent == null || child == null) return -1;
        
        Transform current = child;
        int distance = 0;
        
        while (current != null && distance < 10) // 防止无限循环
        {
            if (current == parent)
                return distance;
            current = current.parent;
            distance++;
        }
        
        return -1; // 没有找到父子关系
    }

    private bool IsCrossPrefabReference(GameObject prefab, ParticleSystem subEmitter)
    {
        if (prefab == null || subEmitter == null) return false;
        
        try
        {
            // 检查subEmitter是否在prefab的层级中
            Transform current = subEmitter.transform;
            while (current != null)
            {
                if (current.gameObject == prefab)
                    return false; // 在同一个预制体中
                current = current.parent;
            }
            
            // 如果能获取到AssetPath，进一步确认
            string prefabPath = AssetDatabase.GetAssetPath(prefab);
            string subEmitterPath = AssetDatabase.GetAssetPath(subEmitter.gameObject);
            
            if (!string.IsNullOrEmpty(prefabPath) && !string.IsNullOrEmpty(subEmitterPath))
            {
                return prefabPath != subEmitterPath;
            }
            
            return true; // 不在同一个预制体层级中，可能是跨预制体引用
        }
        catch (System.Exception ex)
        {
            Debug.LogWarning($"检查跨预制体引用时发生错误: {ex.Message}");
            return false;
        }
    }

    private bool FixSubEmitterForPrefab(GameObject prefab)
    {
        try
        {
            if (prefab == null)
            {
                Debug.LogError("prefab参数为null");
                return false;
            }

            string prefabPath = AssetDatabase.GetAssetPath(prefab);
            if (string.IsNullOrEmpty(prefabPath))
            {
                Debug.LogError($"无法获取预制体路径: {prefab.name}");
                return false;
            }

            var prefabStage = UnityEditor.SceneManagement.PrefabStageUtility.OpenPrefab(prefabPath);
            if (prefabStage == null)
            {
                Debug.LogError($"无法打开预制体进行编辑: {prefab.name}");
                return false;
            }

            GameObject prefabRoot = prefabStage.prefabContentsRoot;
            if (prefabRoot == null)
            {
                Debug.LogError($"预制体根对象为null: {prefab.name}");
                UnityEditor.SceneManagement.StageUtility.GoToMainStage();
                return false;
            }

            ParticleSystem[] particleSystems = prefabRoot.GetComponentsInChildren<ParticleSystem>(true);
            if (particleSystems == null || particleSystems.Length == 0)
            {
                Debug.LogError($"预制体中没有找到ParticleSystem组件: {prefab.name}");
                UnityEditor.SceneManagement.StageUtility.GoToMainStage();
                return false;
            }
            
            bool hasFixed = false;
            foreach (var ps in particleSystems)
            {
                if (ps == null) continue;

                var subEmitters = ps.subEmitters;
                if (!subEmitters.enabled) continue;

                List<int> missingIndices = new List<int>();
                for (int i = 0; i < subEmitters.subEmittersCount; i++)
                {
                    try
                    {
                        if (subEmitters.GetSubEmitterSystem(i) == null)
                        {
                            missingIndices.Add(i);
                        }
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogError($"检查SubEmitter索引 {i} 时发生错误: {ex.Message}");
                        missingIndices.Add(i);
                    }
                }

                if (missingIndices.Count > 0)
                {
                    try
                    {
                        SerializedObject serializedPS = new SerializedObject(ps);
                        if (serializedPS == null)
                        {
                            Debug.LogError($"无法创建SerializedObject for {ps.name}");
                            continue;
                        }

                        if (missingIndices.Count == subEmitters.subEmittersCount)
                        {
                            // 全部丢失，禁用SubEmitters
                            SerializedProperty subEmittersModule = serializedPS.FindProperty("SubModule");
                            if (subEmittersModule != null)
                            {
                                SerializedProperty enabledProp = subEmittersModule.FindPropertyRelative("enabled");
                                if (enabledProp != null)
                                {
                                    enabledProp.boolValue = false;
                                    serializedPS.ApplyModifiedProperties();
                                    
                                    Debug.LogError($"已禁用 {ps.name} 的SubEmitters模块（所有引用丢失）");
                                    hasFixed = true;
                                }
                                else
                                {
                                    Debug.LogError($"无法找到SubModule.enabled属性 for {ps.name}");
                                }
                            }
                            else
                            {
                                Debug.LogError($"无法找到SubModule属性 for {ps.name}");
                            }
                        }
                        else
                        {
                            // 部分丢失，清除丢失的引用
                            SerializedProperty subEmittersModule = serializedPS.FindProperty("SubModule");
                            if (subEmittersModule != null)
                            {
                                SerializedProperty subEmittersArray = subEmittersModule.FindPropertyRelative("subEmitters");
                                if (subEmittersArray != null && subEmittersArray.isArray)
                                {
                                    // 从后往前删除，避免索引变化
                                    for (int i = missingIndices.Count - 1; i >= 0; i--)
                                    {
                                        int index = missingIndices[i];
                                        if (index >= 0 && index < subEmittersArray.arraySize)
                                        {
                                            subEmittersArray.DeleteArrayElementAtIndex(index);
                                        }
                                    }
                                    
                                    serializedPS.ApplyModifiedProperties();
                                    Debug.LogError($"已清除 {ps.name} 的SubEmitter丢失引用，索引: [{string.Join(", ", missingIndices)}]");
                                    hasFixed = true;
                                }
                                else
                                {
                                    Debug.LogError($"无法找到SubModule.subEmitters属性或属性不是数组 for {ps.name}");
                                    
                                    SerializedProperty enabledProp = subEmittersModule.FindPropertyRelative("enabled");
                                    if (enabledProp != null)
                                    {
                                        enabledProp.boolValue = false;
                                        serializedPS.ApplyModifiedProperties();
                                        
                                        Debug.LogError($"无法清除特定引用，已禁用 {ps.name} 的整个SubEmitters模块");
                                        hasFixed = true;
                                    }
                                }
                            }
                            else
                            {
                                Debug.LogError($"无法找到SubModule属性 for {ps.name}");
                            }
                        }
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogError($"修复 {ps.name} 的SubEmitter时发生错误: {ex.Message}");
                        Debug.LogException(ex);
                    }
                }
            }

            if (hasFixed)
            {
                try
                {
                    PrefabUtility.SaveAsPrefabAsset(prefabRoot, prefabPath);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                    Debug.LogError($"成功保存预制体修改: {prefab.name}");
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"保存预制体 {prefab.name} 时发生错误: {ex.Message}");
                    Debug.LogException(ex);
                }
            }

            UnityEditor.SceneManagement.StageUtility.GoToMainStage();
            
            return hasFixed;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"修复预制体 {(prefab != null ? prefab.name : "null")} 的SubEmitter时发生错误: {ex.Message}");
            Debug.LogException(ex);
            
            // 确保即使出错也要回到主场景
            try
            {
                UnityEditor.SceneManagement.StageUtility.GoToMainStage();
            }
            catch
            {
                // 忽略返回主场景时的错误
            }
            
            return false;
        }
    }

    private bool FixShapeArcForPrefab(GameObject prefab)
    {
        try
        {
            if (prefab == null)
            {
                Debug.LogError("prefab参数为null");
                return false;
            }

            string prefabPath = AssetDatabase.GetAssetPath(prefab);
            if (string.IsNullOrEmpty(prefabPath))
            {
                Debug.LogError($"无法获取预制体路径: {prefab.name}");
                return false;
            }

            var prefabStage = UnityEditor.SceneManagement.PrefabStageUtility.OpenPrefab(prefabPath);
            if (prefabStage == null)
            {
                Debug.LogError($"无法打开预制体进行编辑: {prefab.name}");
                return false;
            }

            GameObject prefabRoot = prefabStage.prefabContentsRoot;
            if (prefabRoot == null)
            {
                Debug.LogError($"预制体根对象为null: {prefab.name}");
                UnityEditor.SceneManagement.StageUtility.GoToMainStage();
                return false;
            }

            ParticleSystem[] particleSystems = prefabRoot.GetComponentsInChildren<ParticleSystem>(true);
            if (particleSystems == null || particleSystems.Length == 0)
            {
                Debug.LogError($"预制体中没有找到ParticleSystem组件: {prefab.name}");
                UnityEditor.SceneManagement.StageUtility.GoToMainStage();
                return false;
            }
            
            bool hasFixed = false;
            foreach (var ps in particleSystems)
            {
                if (ps == null) continue;

                var shape = ps.shape;
                if (!shape.enabled) continue;

                // 检查支持Arc参数的Shape类型
                if (shape.shapeType == ParticleSystemShapeType.Circle ||
                    shape.shapeType == ParticleSystemShapeType.CircleEdge ||
                    shape.shapeType == ParticleSystemShapeType.Hemisphere ||
                    shape.shapeType == ParticleSystemShapeType.HemisphereShell ||
                    shape.shapeType == ParticleSystemShapeType.Cone ||
                    shape.shapeType == ParticleSystemShapeType.ConeShell ||
                    shape.shapeType == ParticleSystemShapeType.ConeVolume ||
                    shape.shapeType == ParticleSystemShapeType.ConeVolumeShell)
                {
                    if (Mathf.Approximately(shape.arc, 0f))
                    {
                        try
                        {
                            SerializedObject serializedPS = new SerializedObject(ps);
                            if (serializedPS == null)
                            {
                                Debug.LogError($"无法创建SerializedObject for {ps.name}");
                                continue;
                            }

                            SerializedProperty shapeModule = serializedPS.FindProperty("ShapeModule");
                            if (shapeModule != null)
                            {
                                SerializedProperty arcProp = shapeModule.FindPropertyRelative("arc");
                                if (arcProp != null)
                                {
                                    if (arcProp.propertyType == SerializedPropertyType.Generic)
                                    {
                                        SerializedProperty valueProp = arcProp.FindPropertyRelative("value");
                                        if (valueProp != null && valueProp.propertyType == SerializedPropertyType.Float)
                                        {
                                            float currentValue = valueProp.floatValue;
                                            valueProp.floatValue = 0.1f;
                                            serializedPS.ApplyModifiedProperties();
                                            hasFixed = true;
                                        }
                                    }
                                    else
                                    {
                                        Debug.LogError($"Arc属性类型不支持: {arcProp.propertyType} for {ps.name}");
                                    }
                                }
                            }
                        }
                        catch (System.Exception ex)
                        {
                            Debug.LogError($"修复 {ps.name} 的Shape Arc时发生错误: {ex.Message}");
                            Debug.LogException(ex);
                        }
                    }
                }
            }

            if (hasFixed)
            {
                try
                {
                    PrefabUtility.SaveAsPrefabAsset(prefabRoot, prefabPath);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"保存预制体 {prefab.name} 时发生错误: {ex.Message}");
                    Debug.LogException(ex);
                }
            }

            UnityEditor.SceneManagement.StageUtility.GoToMainStage();
            
            return hasFixed;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"修复预制体 {(prefab != null ? prefab.name : "null")} 的Shape Arc时发生错误: {ex.Message}");
            Debug.LogException(ex);
            
            // 确保即使出错也要回到主场景
            try
            {
                UnityEditor.SceneManagement.StageUtility.GoToMainStage();
            }
            catch
            {
                // 忽略返回主场景时的错误
            }
            
            return false;
        }
    }
#endregion

#region 检查材质
    private void CheckMissingMaterials(GameObject prefab, ParticleSystem ps)
    {
        var renderer = ps.GetComponent<ParticleSystemRenderer>();
        if (renderer == null || !renderer.enabled) return;

        try
        {
            // 检测主要的粒子材质
            if (renderer.renderMode != ParticleSystemRenderMode.None)
            {
                if (renderer.sharedMaterial == null)
                {
                    AddIssue("材质异常", "材质丢失", prefab, ps.gameObject, ps,
                        "ParticleSystemRenderer的材质引用为空");
                }
                else
                {
                    // 检查是否使用Unity内置材质
                    if (IsUnityBuiltInMaterial(renderer.sharedMaterial))
                    {
                        AddIssue("材质异常", "使用Unity内置材质", prefab, ps.gameObject, ps,
                            $"ParticleSystemRenderer使用了Unity内置材质: {renderer.sharedMaterial.name}");
                    }
                    
                    // 检查是否使用Unity内置Shader
                    if (IsUnityBuiltInShader(renderer.sharedMaterial.shader))
                    {
                        AddIssue("材质异常", "使用Unity内置Shader", prefab, ps.gameObject, ps,
                            $"材质 '{renderer.sharedMaterial.name}' 使用了Unity内置Shader: {renderer.sharedMaterial.shader.name}");
                    }
                }
            }
            else
            {
                if (renderer.sharedMaterial != null)
                {
                    AddIssue("材质异常", $"材质多引用 {renderer.sharedMaterial.name}", prefab, ps.gameObject, ps,
                        "ParticleSystemRenderer该renderMode不挂材质，但是其他renderMode的材质缓存还在");
                }
            }

            // 检测trails模块的材质
            var trails = ps.trails;
            if (trails.enabled)
            {
                if (renderer.trailMaterial == null)
                {
                    AddIssue("材质异常", "Trail材质丢失", prefab, ps.gameObject, ps,
                        "Trails模块已启用但trailMaterial引用为空");
                }
                else
                {
                    // 检查Trail材质是否使用Unity内置材质
                    if (IsUnityBuiltInMaterial(renderer.trailMaterial))
                    {
                        AddIssue("材质异常", "Trail使用Unity内置材质", prefab, ps.gameObject, ps,
                            $"Trail使用了Unity内置材质: {renderer.trailMaterial.name}");
                    }
                    
                    // 检查Trail材质是否使用Unity内置Shader
                    if (IsUnityBuiltInShader(renderer.trailMaterial.shader))
                    {
                        AddIssue("材质异常", "Trail使用Unity内置Shader", prefab, ps.gameObject, ps,
                            $"Trail材质 '{renderer.trailMaterial.name}' 使用了Unity内置Shader: {renderer.trailMaterial.shader.name}");
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogWarning($"检测材质时发生错误: {ex.Message}");
        }
    }

    private bool IsUnityBuiltInMaterial(Material material)
    {
        if (material == null) return false;
        
        try
        {
            string assetPath = AssetDatabase.GetAssetPath(material);
            
            // Unity内置材质的路径特征
            if (string.IsNullOrEmpty(assetPath) || 
                assetPath.Contains("Library/unity default resources") || 
                assetPath.Contains("Built-in Material") ||
                assetPath.Contains("unity_builtin_extra") ||
                assetPath.StartsWith("Resources/unity_builtin"))
            {
                return true;
            }
            
            // 检查常见的Unity内置材质名称
            string materialName = material.name.ToLower();
            if (materialName.Contains("default") || 
                materialName.Contains("sprites-default") ||
                materialName.Contains("ui-default") ||
                materialName.Contains("particle") && materialName.Contains("default") ||
                materialName == "default-material" ||
                materialName == "default-particle" ||
                materialName == "default-line")
            {
                return true;
            }
            
            return false;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检查材质是否为内置资源时发生错误: {ex.Message}");
            return false;
        }
    }

    private bool IsUnityBuiltInShader(Shader shader)
    {
        if (shader == null) return false;
        
        try
        {
            string shaderName = shader.name;
            
            // URP内置Shader
            if (shaderName.StartsWith("Universal Render Pipeline/") ||
                shaderName.StartsWith("Shader Graphs/"))
            {
                return true;
            }
            
            // 传统内置Shader
            if (shaderName.StartsWith("Standard") ||
                shaderName.StartsWith("Legacy Shaders/") ||
                shaderName.StartsWith("Mobile/") ||
                shaderName.StartsWith("Unlit/") ||
                shaderName.StartsWith("Sprites/") ||
                shaderName.StartsWith("UI/") ||
                shaderName.StartsWith("Particles/") ||
                shaderName.StartsWith("VR/") ||
                shaderName.StartsWith("Skybox/") ||
                shaderName.StartsWith("Nature/") ||
                shaderName.StartsWith("FX/"))
            {
                return true;
            }
            
            // 其他常见内置Shader
            if (shaderName == "Diffuse" ||
                shaderName == "Specular" ||
                shaderName == "Bumped Diffuse" ||
                shaderName == "Bumped Specular" ||
                shaderName.Contains("Hidden/"))
            {
                return true;
            }
            
            return false;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检查Shader是否为内置资源时发生错误: {ex.Message}");
            return false;
        }
    }
#endregion

#region 检查贴图
    private void CheckMissingTextures(GameObject prefab, ParticleSystem ps)
    {
        var renderer = ps.GetComponent<ParticleSystemRenderer>();
        if (renderer == null || renderer.sharedMaterial == null || !renderer.enabled) return;

        var material = renderer.sharedMaterial;
        var shader = material.shader;
        
        if (shader == null) return;
        
        try
        {
            for (int i = 0; i < ShaderUtil.GetPropertyCount(shader); i++)
            {
                if (ShaderUtil.GetPropertyType(shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                {
                    string propertyName = ShaderUtil.GetPropertyName(shader, i);
                    if (string.IsNullOrEmpty(propertyName)) continue;
                    
                    Texture texture = material.GetTexture(propertyName);
                    
                    // 安全地获取属性描述，避免空引用
                    string propertyDescription = "";
                    try
                    {
                        propertyDescription = ShaderUtil.GetPropertyDescription(shader, i) ?? "";
                    }
                    catch
                    {
                        propertyDescription = "";
                    }
                    
                    if (texture == null && propertyDescription.Contains("Main"))
                    {
                        AddIssue("贴图异常", "主贴图丢失", prefab, ps.gameObject, ps,
                            $"材质 '{material.name}' 的主贴图 '{propertyName}' 为空");
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检测材质 '{material.name}' 的贴图时发生错误: {ex.Message}");
        }
    }
#endregion

#region 检查Mesh
    private void CheckMissingMesh(GameObject prefab, ParticleSystem ps)
    {
        var renderer = ps.GetComponent<ParticleSystemRenderer>();
        if(renderer.enabled)
        {
            if (renderer.renderMode == ParticleSystemRenderMode.Mesh)
            {
                if (renderer.mesh == null)
                {
                    AddIssue("Rederer Mesh异常", "Mesh丢失", prefab, ps.gameObject, ps,
                        "ParticleSystemRenderer的Mesh引用为空");
                }
                else if (IsUnityBuiltInMesh(renderer.mesh))
                {
                    AddIssue("Rederer Mesh异常", "使用Unity内置Mesh", prefab, ps.gameObject, ps,
                        $"ParticleSystemRenderer使用了Unity内置Mesh: {renderer.mesh.name}");
                }
            }
        }
        else
        {
            if (renderer.mesh != null)
            {
                AddIssue("Rederer Mesh异常", $"Mesh无效引用 {renderer.mesh.name}", prefab, ps.gameObject, ps,
                    "ParticleSystemRenderer的未开启，但是Mesh缓存还在");
            }
        }

        var shape = ps.shape;
        if (shape.enabled)
        {
            if (shape.shapeType == ParticleSystemShapeType.Mesh || shape.shapeType == ParticleSystemShapeType.MeshRenderer || shape.shapeType == ParticleSystemShapeType.SkinnedMeshRenderer)
            {
                if (shape.mesh == null)
                {
                    AddIssue("Shape Mesh异常", "Mesh丢失", prefab, ps.gameObject, ps,
                        "Shape的Mesh引用为空");
                }
                else if (IsUnityBuiltInMesh(shape.mesh))
                {
                    AddIssue("Shape Mesh异常", "使用Unity内置Mesh", prefab, ps.gameObject, ps,
                        $"Shape使用了Unity内置Mesh: {shape.mesh.name}");
                }
            }
        }
        else
        {
            if (shape.mesh != null)
            {
                AddIssue("Rederer Mesh异常", "Mesh无效引用", prefab, ps.gameObject, ps,
                    "Shape的未开启，但是Mesh缓存还在");
            }
        }
    }

    private bool IsUnityBuiltInMesh(Mesh mesh)
    {
        if (mesh == null) return false;
        
        try
        {
            string assetPath = AssetDatabase.GetAssetPath(mesh);
            
            // Unity内置资源的路径特征
            if (string.IsNullOrEmpty(assetPath) || 
                assetPath.Contains("Library/unity default resources") || 
                assetPath.Contains("Built-in Mesh") ||
                assetPath.Contains("unity_builtin_extra") ||
                assetPath.StartsWith("Resources/unity_builtin"))
            {
                return true;
            }
            
            // 检查常见的Unity内置mesh名称
            string meshName = mesh.name.ToLower();
            if (meshName == "cube" || 
                meshName == "sphere" || 
                meshName == "capsule" || 
                meshName == "cylinder" || 
                meshName == "plane" || 
                meshName == "quad" ||
                meshName.Contains("primitive"))
            {
                return true;
            }
            
            return false;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检查Mesh是否为内置资源时发生错误: {ex.Message}");
            return false;
        }
    }
#endregion

#region 检查循环播放但Duration为0
    private void CheckLoopingWithZeroDuration(GameObject prefab, ParticleSystem ps)
    {
        var main = ps.main;
        if (main.loop && main.duration <= 0.01f)
        {
            AddIssue("配置异常", "循环播放但Duration接近0", prefab, ps.gameObject, ps,
                $"粒子系统设置为循环播放但持续时间只有 {main.duration:F3} 秒");
        }
    }
#endregion

#region 检查Shape Arc为0
    private void CheckShapeArcZero(GameObject prefab, ParticleSystem ps)
    {
        var shape = ps.shape;
        if (shape.enabled)
        {
            // 检查支持Arc参数的Shape类型
            if (shape.shapeType == ParticleSystemShapeType.Circle ||
                shape.shapeType == ParticleSystemShapeType.CircleEdge ||
                shape.shapeType == ParticleSystemShapeType.Hemisphere ||
                shape.shapeType == ParticleSystemShapeType.HemisphereShell ||
                shape.shapeType == ParticleSystemShapeType.Cone ||
                shape.shapeType == ParticleSystemShapeType.ConeShell ||
                shape.shapeType == ParticleSystemShapeType.ConeVolume ||
                shape.shapeType == ParticleSystemShapeType.ConeVolumeShell)
            {
                if (Mathf.Approximately(shape.arc, 0f))
                {
                    AddIssue("Shape配置异常", "Shape Arc为0", prefab, ps.gameObject, ps,
                        $"Shape类型 '{shape.shapeType}' 的Arc设置为0，这可能导致粒子无法正常发射");
                }
            }
        }
    }
#endregion
#endregion

#region 性能检测
#region 检查MaxParticles
    private void CheckMaxParticles(GameObject prefab, ParticleSystem ps)
    {
        var main = ps.main;
        if (main.maxParticles > maxParticlesThreshold)
        {
            AddIssue("性能问题", "MaxParticles过大", prefab, ps.gameObject, ps,
                $"MaxParticles设置为 {main.maxParticles}，超过阈值 {maxParticlesThreshold}");
        }
    }
#endregion

#region 检查预热时间
    private void CheckPrewarmTime(GameObject prefab, ParticleSystem ps)
    {
        var main = ps.main;
        if (main.prewarm && main.duration > maxPrewarmTimeThreshold)
        {
            AddIssue("性能问题", "预热时间过长", prefab, ps.gameObject, ps,
                $"预热时间设置为 {main.duration} 秒，超过阈值 {maxPrewarmTimeThreshold} 秒");
        }
    }
#endregion

#region 检查生命周期
    private void CheckLifetime(GameObject prefab, ParticleSystem ps)
    {
        var main = ps.main;
        float maxLifetime = main.startLifetime.mode == ParticleSystemCurveMode.Constant 
            ? main.startLifetime.constant 
            : main.startLifetime.constantMax;

        if (maxLifetime > maxLifetimeThreshold)
        {
            AddIssue("性能问题", "生命周期过长", prefab, ps.gameObject, ps,
                $"粒子生命周期为 {maxLifetime:F2} 秒，超过阈值 {maxLifetimeThreshold:F2} 秒");
        }
    }
#endregion

#region 检查渲染队列
    private void CheckRenderQueue(GameObject prefab, ParticleSystem ps)
    {
        var renderer = ps.GetComponent<ParticleSystemRenderer>();
        if (renderer == null || renderer.sharedMaterial == null || !renderer.enabled) return;

        try
        {
            int renderQueue = renderer.sharedMaterial.renderQueue;
            
            // 检查是否在透明队列范围内但使用了不透明混合模式
            if (renderQueue >= 3000 && renderQueue < 4000) // Transparent queue
            {
                // 安全地获取_Mode属性，避免材质没有这个属性
                if (renderer.sharedMaterial.HasProperty("_Mode"))
                {
                    if (renderer.sharedMaterial.GetFloat("_Mode") == 0) // Opaque mode
                    {
                        AddIssue("渲染异常", "渲染队列配置异常", prefab, ps.gameObject, ps,
                            $"材质使用透明队列 ({renderQueue}) 但配置为不透明模式");
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检测渲染队列时发生错误 (材质: {renderer.sharedMaterial?.name}): {ex.Message}");
        }
    }
#endregion

#region 检查动画曲线
    private void CheckAnimationCurves(GameObject prefab, ParticleSystem ps)
    {
        try
        {
            var main = ps.main;
            
            CheckCurveKeyframes(prefab, ps, main.startSize, "StartSize");
            CheckCurveKeyframes(prefab, ps, main.startSpeed, "StartSpeed");
            CheckCurveKeyframes(prefab, ps, main.startLifetime, "StartLifetime");
            
            var velocityOverLifetime = ps.velocityOverLifetime;
            if (velocityOverLifetime.enabled)
            {
                CheckCurveKeyframes(prefab, ps, velocityOverLifetime.x, "VelocityOverLifetime.X");
                CheckCurveKeyframes(prefab, ps, velocityOverLifetime.y, "VelocityOverLifetime.Y");
                CheckCurveKeyframes(prefab, ps, velocityOverLifetime.z, "VelocityOverLifetime.Z");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检测动画曲线时发生错误: {ex.Message}");
        }
    }
#endregion

#region 检查关键帧
    private void CheckCurveKeyframes(GameObject prefab, ParticleSystem ps, ParticleSystem.MinMaxCurve curve, string curveName)
    {
        try
        {
            if (curve.mode == ParticleSystemCurveMode.Curve && curve.curve != null)
            {
                if (curve.curve.keys.Length > maxKeyframesThreshold)
                {
                    AddIssue("性能问题", "动画曲线关键帧过多", prefab, ps.gameObject, ps,
                        $"{curveName} 曲线有 {curve.curve.keys.Length} 个关键帧，超过阈值 {maxKeyframesThreshold}");
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检测曲线 {curveName} 时发生错误: {ex.Message}");
        }
    }
#endregion
#endregion

    private void AddIssue(string issueType, string issueName, GameObject prefab, GameObject particleObject, 
        ParticleSystem ps, string description)
    {
        detectedIssues.Add(new ParticleSystemIssue
        {
            issueType = issueType,
            issueName = issueName,
            prefab = prefab,
            particleObject = particleObject,
            particleSystem = ps,
            description = description
        });
    }

    [Button("清除检测结果", ButtonSizes.Medium)]
    [GUIColor(1f, 0.6f, 0.6f)]
    public void ClearResults()
    {
        detectedIssues.Clear();
    }

    [Button("诊断ParticleSystem属性结构", ButtonSizes.Medium)]
    [GUIColor(0.8f, 0.8f, 1f)]
    public void DiagnoseParticleSystemProperties()
    {
        if (individualPrefabs == null || individualPrefabs.Length == 0)
        {
            Debug.LogError("请先设置一个要诊断的预制体！");
            return;
        }

        GameObject prefab = individualPrefabs[0];
        if (prefab == null)
        {
            Debug.LogError("预制体为null！");
            return;
        }

        ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>(true);
        if (particleSystems == null || particleSystems.Length == 0)
        {
            Debug.LogError("预制体中没有ParticleSystem组件！");
            return;
        }

        ParticleSystem ps = particleSystems[0];
        SerializedObject serializedPS = new SerializedObject(ps);
        
        Debug.LogError("=== ParticleSystem属性结构诊断 ===");
        Debug.LogError($"预制体: {prefab.name}, ParticleSystem: {ps.name}");
        
        SerializedProperty iterator = serializedPS.GetIterator();
        bool enterChildren = true;
        int depth = 0;
        
        while (iterator.NextVisible(enterChildren))
        {
            string indent = new string(' ', depth * 2);
            string propertyInfo = $"{indent}{iterator.propertyPath} ({iterator.propertyType})";
            
            if (iterator.propertyPath.Contains("Sub") || iterator.propertyPath.Contains("sub"))
            {
                Debug.LogError($"[SubEmitter相关] {propertyInfo}");
            }
            else
            {
                Debug.Log(propertyInfo);
            }
            
            enterChildren = false;
            depth = iterator.depth;
        }
        
        Debug.LogError("=== 诊断完成 ===");
    }

    [Button("诊断Shape模块属性结构", ButtonSizes.Medium)]
    [GUIColor(1f, 0.8f, 0.8f)]
    public void DiagnoseShapeModuleProperties()
    {
        if (individualPrefabs == null || individualPrefabs.Length == 0)
        {
            Debug.LogError("请先设置一个要诊断的预制体！");
            return;
        }

        GameObject prefab = individualPrefabs[0];
        if (prefab == null)
        {
            Debug.LogError("预制体为null！");
            return;
        }

        ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>(true);
        if (particleSystems == null || particleSystems.Length == 0)
        {
            Debug.LogError("预制体中没有ParticleSystem组件！");
            return;
        }

        ParticleSystem ps = particleSystems[0];
        SerializedObject serializedPS = new SerializedObject(ps);
        
        Debug.LogError("=== Shape模块属性结构诊断 ===");
        Debug.LogError($"预制体: {prefab.name}, ParticleSystem: {ps.name}");
        Debug.LogError($"Shape模块启用: {ps.shape.enabled}");
        Debug.LogError($"Shape类型: {ps.shape.shapeType}");
        Debug.LogError($"实际Arc值: {ps.shape.arc}");
        
        // 查找Shape模块属性
        SerializedProperty shapeModule = serializedPS.FindProperty("ShapeModule");
        if (shapeModule != null)
        {
            Debug.LogError("找到ShapeModule属性");
            Debug.LogError($"ShapeModule类型: {shapeModule.propertyType}");
            
            // 遍历Shape模块的所有子属性
            SerializedProperty iterator = shapeModule.Copy();
            bool enterChildren = true;
            int depth = shapeModule.depth;
            
            while (iterator.NextVisible(enterChildren) && iterator.depth > depth)
            {
                string indent = new string(' ', (iterator.depth - depth - 1) * 2);
                string propertyInfo = $"{indent}{iterator.name} (路径: {iterator.propertyPath}, 类型: {iterator.propertyType})";
                
                if (iterator.name.ToLower().Contains("arc") || iterator.propertyPath.ToLower().Contains("arc"))
                {
                    Debug.LogError($"[Arc相关] {propertyInfo}");
                    if (iterator.propertyType == SerializedPropertyType.Float)
                    {
                        Debug.LogError($"  当前值: {iterator.floatValue}");
                    }
                    else if (iterator.propertyType == SerializedPropertyType.Integer)
                    {
                        Debug.LogError($"  当前值: {iterator.intValue}");
                    }
                    else if (iterator.propertyType == SerializedPropertyType.Generic)
                    {
                        Debug.LogError($"  这是Generic类型，查看其子属性:");
                        
                        // 深入探索Generic类型的子属性
                        SerializedProperty arcIterator = iterator.Copy();
                        bool enterArcChildren = true;
                        int arcDepth = iterator.depth;
                        
                        while (arcIterator.NextVisible(enterArcChildren) && arcIterator.depth > arcDepth)
                        {
                            string arcIndent = new string(' ', (arcIterator.depth - arcDepth) * 4);
                            Debug.LogError($"    {arcIndent}{arcIterator.name} (路径: {arcIterator.propertyPath}, 类型: {arcIterator.propertyType})");
                            
                            if (arcIterator.propertyType == SerializedPropertyType.Float)
                            {
                                Debug.LogError($"    {arcIndent}  当前值: {arcIterator.floatValue}");
                            }
                            else if (arcIterator.propertyType == SerializedPropertyType.Integer)
                            {
                                Debug.LogError($"    {arcIndent}  当前值: {arcIterator.intValue}");
                            }
                            else if (arcIterator.propertyType == SerializedPropertyType.Enum)
                            {
                                Debug.LogError($"    {arcIndent}  枚举值: {arcIterator.enumValueIndex} ({arcIterator.enumNames[arcIterator.enumValueIndex]})");
                            }
                            
                            enterArcChildren = false;
                        }
                    }
                    else
                    {
                        Debug.LogError($"  值: {iterator.ToString()}");
                    }
                }
                else
                {
                    Debug.Log(propertyInfo);
                }
                
                enterChildren = false;
            }
        }
        else
        {
            Debug.LogError("未找到ShapeModule属性");
        }
        
        Debug.LogError("=== Shape诊断完成 ===");
    }

    [Button("高级SubEmitter层级诊断", ButtonSizes.Medium)]
    [GUIColor(0.8f, 1f, 0.8f)]
    public void AdvancedSubEmitterDiagnosis()
    {
        if (individualPrefabs == null || individualPrefabs.Length == 0)
        {
            Debug.LogError("请先设置一个要诊断的预制体！");
            return;
        }

        GameObject prefab = individualPrefabs[0];
        if (prefab == null)
        {
            Debug.LogError("预制体为null！");
            return;
        }

        Debug.LogError("=== 高级SubEmitter层级诊断 ===");
        Debug.LogError($"预制体: {prefab.name}");
        Debug.LogError($"预制体路径: {AssetDatabase.GetAssetPath(prefab)}");
        
        ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>(true);
        if (particleSystems == null || particleSystems.Length == 0)
        {
            Debug.LogError("预制体中没有ParticleSystem组件！");
            return;
        }

        Debug.LogError($"发现 {particleSystems.Length} 个ParticleSystem组件");
        
        for (int psIndex = 0; psIndex < particleSystems.Length; psIndex++)
        {
            var ps = particleSystems[psIndex];
            if (ps == null) continue;
            
            Debug.LogError($"\n--- ParticleSystem {psIndex + 1}: {ps.name} ---");
            Debug.LogError($"GameObject路径: {GetGameObjectPath(ps.transform, prefab.transform)}");
            Debug.LogError($"GameObject激活状态: {ps.gameObject.activeSelf}");
            Debug.LogError($"发射模块启用状态: {ps.emission.enabled}");
            
            var subEmitters = ps.subEmitters;
            Debug.LogError($"SubEmitters模块启用: {subEmitters.enabled}");
            Debug.LogError($"SubEmitters数量: {subEmitters.subEmittersCount}");
            
            if (subEmitters.enabled && subEmitters.subEmittersCount > 0)
            {
                for (int i = 0; i < subEmitters.subEmittersCount; i++)
                {
                    Debug.LogError($"\n  SubEmitter {i}:");
                    
                    try
                    {
                        ParticleSystem subEmitter = subEmitters.GetSubEmitterSystem(i);
                        if (subEmitter == null)
                        {
                            Debug.LogError($"    状态: 引用为null");
                        }
                        else
                        {
                            Debug.LogError($"    名称: {subEmitter.name}");
                            Debug.LogError($"    路径: {GetGameObjectPath(subEmitter.transform, prefab.transform)}");
                            Debug.LogError($"    激活状态: {subEmitter.gameObject.activeSelf}");
                            Debug.LogError($"    发射模块启用状态: {subEmitter.emission.enabled}");
                            
                            // 检查层级关系的详细信息
                            bool isChild = subEmitter.transform.IsChildOf(ps.transform);
                            Debug.LogError($"    是ps的子级: {isChild}");
                            
                            if (!isChild)
                            {
                                // 详细分析为什么不是子级
                                Debug.LogError($"    [问题分析]");
                                
                                Transform psRoot = ps.transform.root;
                                Transform subRoot = subEmitter.transform.root;
                                Debug.LogError($"    ps根对象: {psRoot.name}");
                                Debug.LogError($"    sub根对象: {subRoot.name}");
                                Debug.LogError($"    同根对象: {psRoot == subRoot}");
                                
                                if (psRoot == subRoot)
                                {
                                    // 在同一个根对象下，但不是父子关系
                                    if (ps.transform.parent == subEmitter.transform.parent)
                                    {
                                        Debug.LogError($"    问题: 是兄弟关系，应改为父子关系");
                                    }
                                    else if (ps.transform.IsChildOf(subEmitter.transform))
                                    {
                                        Debug.LogError($"    问题: 层级关系颠倒，ps是sub的子级");
                                    }
                                    else
                                    {
                                        // 计算最近公共祖先
                                        Transform commonAncestor = FindCommonAncestor(ps.transform, subEmitter.transform);
                                        if (commonAncestor != null)
                                        {
                                            Debug.LogError($"    最近公共祖先: {commonAncestor.name}");
                                            Debug.LogError($"    ps到公共祖先距离: {GetDistanceToAncestor(ps.transform, commonAncestor)}");
                                            Debug.LogError($"    sub到公共祖先距离: {GetDistanceToAncestor(subEmitter.transform, commonAncestor)}");
                                        }
                                    }
                                }
                                else
                                {
                                    Debug.LogError($"    问题: 可能是跨预制体引用");
                                    
                                    string psPath = AssetDatabase.GetAssetPath(ps.gameObject);
                                    string subPath = AssetDatabase.GetAssetPath(subEmitter.gameObject);
                                    Debug.LogError($"    ps资源路径: {psPath}");
                                    Debug.LogError($"    sub资源路径: {subPath}");
                                }
                            }
                            
                            // 检查SubEmitter的SubEmitter
                            var subSubEmitters = subEmitter.subEmitters;
                            if (subSubEmitters.enabled && subSubEmitters.subEmittersCount > 0)
                            {
                                Debug.LogError($"    子SubEmitter数量: {subSubEmitters.subEmittersCount}");
                            }
                        }
                        
                        // 获取SubEmitter的类型和属性
                        var subEmitterType = subEmitters.GetSubEmitterType(i);
                        var subEmitterProperties = subEmitters.GetSubEmitterProperties(i);
                        Debug.LogError($"    触发类型: {subEmitterType}");
                        Debug.LogError($"    属性: {subEmitterProperties}");
                        
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogError($"    访问SubEmitter时发生异常: {ex.Message}");
                    }
                }
            }
        }
        
        Debug.LogError("\n=== 高级诊断完成 ===");
    }

    private Transform FindCommonAncestor(Transform t1, Transform t2)
    {
        if (t1 == null || t2 == null) return null;
        
        // 收集t1的所有祖先
        HashSet<Transform> ancestors1 = new HashSet<Transform>();
        Transform current = t1;
        while (current != null)
        {
            ancestors1.Add(current);
            current = current.parent;
        }
        
        // 找到t2路径上第一个出现在t1祖先中的Transform
        current = t2;
        while (current != null)
        {
            if (ancestors1.Contains(current))
                return current;
            current = current.parent;
        }
        
        return null;
    }

    private int GetDistanceToAncestor(Transform child, Transform ancestor)
    {
        if (child == null || ancestor == null) return -1;
        
        int distance = 0;
        Transform current = child;
        
        while (current != null && current != ancestor && distance < 20)
        {
            current = current.parent;
            distance++;
        }
        
        return current == ancestor ? distance : -1;
    }

    [Button("导出检测报告", ButtonSizes.Medium)]
    [GUIColor(0.6f, 1f, 0.6f)]
    public void ExportReport()
    {
        if (detectedIssues.Count == 0)
        {
            Debug.LogError("没有检测到任何问题，无需导出报告。");
            return;
        }

        string reportPath = EditorUtility.SaveFilePanel("保存检测报告", "", "ParticleSystem检测报告", "txt");
        if (string.IsNullOrEmpty(reportPath)) return;

        using (StreamWriter writer = new StreamWriter(reportPath))
        {
            writer.WriteLine("=== ParticleSystem异常检测报告 ===");
            writer.WriteLine($"检测时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            writer.WriteLine($"检测到问题总数: {detectedIssues.Count}");
            writer.WriteLine();

            var groupedIssues = detectedIssues.GroupBy(i => i.issueType);
            foreach (var group in groupedIssues)
            {
                writer.WriteLine($"=== {group.Key} ({group.Count()}个问题) ===");
                foreach (var issue in group)
                {
                    writer.WriteLine($"预制体: {AssetDatabase.GetAssetPath(issue.prefab)}");
                    writer.WriteLine($"对象: {issue.particleObject.name}");
                    writer.WriteLine($"问题: {issue.issueName}");
                    writer.WriteLine($"详情: {issue.description}");
                    writer.WriteLine();
                }
                writer.WriteLine();
            }
        }

        Debug.LogError($"检测报告已导出到: {reportPath}");
        System.Diagnostics.Process.Start(reportPath);
    }
}
#endif 
﻿using LuaInterface;
using Nirvana;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering.Universal;

public class DeveloperWindow : OdinEditorWindow
{
    #region 枚举定义
    public enum CharacterType
    {
        随机,
        女刺,
        男剑,
        女剑,
        男法,
        女法
    }

    public enum CharacterNumber
    {
        角色1 = 1,
        角色2 = 2,
    }

    public enum getviewindex
    {
        a_灵宠 = 0,
        a_幻化坐骑 = 1,
        a_羽翼 = 2,
        a_法宝 = 3,
        a_武器 = 4,
        a_灵弓 = 5,
        a_宝宝 = 6,
        a_小鬼 = 7,
        a_境界等级 = 8,
        a_战斗坐骑 = 9,
        a_灵骑 = 10,
        a_灵翼 = 11,
        a_时装 = 12,
        a_足迹 = 13,
        a_光环 = 14,
        a_灵宠正常进阶的灵宠 = 15,
        a_角色面板中的4个模型 = 16,
        a_战斗坐骑2 = 17,
        a_进阶坐骑 = 18,
        a_变身特殊形象和变身初始形象 = 19,
        a_圣器 = 20,
        a_天兵 = 21,
        a_飞宠 = 22,
        a_天神神器 = 28,
        a_天神神器外观 = 29,
    }
    #endregion

    #region 字段声明
    [BoxGroup("快速登录")]
    [LabelText("用户名")]
    [DisableIf("@EditorApplication.isPlaying")]
    public string user_name = "";
    
    [BoxGroup("快速登录")]
    [LabelText("服务器ID")]
    [DisableIf("@EditorApplication.isPlaying")]
    public string server_id = "";
    
    [BoxGroup("快速登录")]
    [HorizontalGroup("快速登录/主区域")]
    [VerticalGroup("快速登录/主区域/左侧")]
    [LabelText("职业选择(无角色)")]
    [ShowIf("@!EditorApplication.isPlaying")]
    public CharacterType characterType = CharacterType.随机;
    
    [BoxGroup("快速登录")]
    [HorizontalGroup("快速登录/主区域")]
    [VerticalGroup("快速登录/主区域/左侧")]
    [LabelText("角色选择(有角色)")]
    [ShowIf("@!EditorApplication.isPlaying")]
    public CharacterNumber characterNumber = CharacterNumber.角色1;

    [BoxGroup("GM命令")]
    [LabelText("GM命令")]
    [DelayedProperty]
    public string gmContent = "";

    // GM命令历史记录相关字段
    private List<string> gmCommandHistory = new List<string>();
    private int currentHistoryIndex = -1;
    private bool isNavigatingHistory = false;
    private string currentEditingCommand = "";

    // 私有字段
    private string previousUserName = "";
    private string previousServerId = "";
    private bool init = false;
    private Vector2 scrollPosition = Vector2.zero;
    
    [TitleGroup("JY Shader调试器")]
    [InlineProperty]
    [HideLabel]
    [PropertyOrder(2000)]
    public UnityEngine.Rendering.Universal.JYShaderDebuggerSettings shaderDebuggerSettings = new UnityEngine.Rendering.Universal.JYShaderDebuggerSettings();
    #endregion

    #region 窗口管理
    [MenuItem("Window/Yifan/Developer")]
    private static void ShowQuickLoginView()
    {
        EditorWindow.GetWindow<DeveloperWindow>(false, "Developer");
    }
    #endregion

    #region 快速登录功能
    [BoxGroup("快速登录")]
    [HorizontalGroup("快速登录/主区域")]
    [VerticalGroup("快速登录/主区域/右侧")]
    [Button("快速登录", ButtonSizes.Large)]
    [ShowIf("@!EditorApplication.isPlaying")]
    [GUIColor(0.4f, 0.8f, 1f)]
    public void QuickLogin()
    {
        SetUserName();
        var (sex, prof) = GetCharacterData(characterType);
        UnityEngine.PlayerPrefs.SetString("a3_fanli_is_quick_login", "1");
        UnityEngine.PlayerPrefs.SetInt("a3_fanli_is_quick_login_sex", sex);
        UnityEngine.PlayerPrefs.SetInt("a3_fanli_is_quick_login_prof", prof);
        UnityEngine.PlayerPrefs.SetInt("a3_fanli_is_quick_login_role_seq", (int)characterNumber);
        EditorApplication.ExecuteMenuItem("Edit/Play");
        characterType = CharacterType.随机;
    }

    [BoxGroup("快速登录")]
    [HorizontalGroup("快速登录/主区域")]
    [Button("快速重启", ButtonSizes.Large)]
    [ShowIf("@EditorApplication.isPlaying")]
    [GUIColor(1f, 0.6f, 0.4f)]
    public void QuickRestart()
    {
        SetUserName();
        DeveloperQuickRestart.Restart();
    }
    #endregion

    #region GM命令功能
    [BoxGroup("GM命令")]
    [Button("发送命令", ButtonSizes.Large)]
    [GUIColor(0.5f, 1f, 0.5f)]
    public void ExecuteGmCommand()
    {
        if (string.IsNullOrEmpty(gmContent))
        {
            if (gmCommandHistory.Count > 0) 
            {
                gmContent = gmCommandHistory[0];
            }
        }
        
        // 保存到历史记录
        SaveToCommandHistory(gmContent);
        ExecuteGm(gmContent);
    }

    private void ExecuteGm(string str)
    {
        if (Application.isPlaying)
            GameRoot.Instance.ExecuteGm(str);
    }

    [BoxGroup("GM命令")]
    [HorizontalGroup("GM命令/历史操作")]
    [Button("↑ 往前翻")]
    [GUIColor(0.7f, 0.9f, 1f)]
    public void PreviousCommand()
    {
        NavigateHistory(1);
    }

    [BoxGroup("GM命令")]
    [HorizontalGroup("GM命令/历史操作")]
    [Button("↓ 往后翻")]
    [GUIColor(0.7f, 0.9f, 1f)]
    public void NextCommand()
    {
        NavigateHistory(-1);
    }

    [BoxGroup("GM命令")]
    [HorizontalGroup("GM命令/历史操作")]
    [Button("清空历史")]
    [GUIColor(1f, 0.7f, 0.7f)]
    public void ClearCommandHistory()
    {
        gmCommandHistory.Clear();
        SaveCommandHistory();
        gmContent = "";
        ResetHistoryNavigation();
    }

    [BoxGroup("GM命令")]
    [ShowInInspector]
    [DisplayAsString]
    [LabelText("历史记录状态")]
    private string HistoryStatus
    {
        get
        {
            if (gmCommandHistory.Count == 0)
                return "无历史记录";
            
            string status = $"共 {gmCommandHistory.Count} 条记录";
            if (isNavigatingHistory)
            {
                if (currentHistoryIndex == -1)
                    status += " (当前: 新输入)";
                else
                    status += $" (当前: 第 {gmCommandHistory.Count - currentHistoryIndex} 条)";
            }
            return status;
        }
    }

    [TitleGroup("常用GM")]
    [HorizontalGroup("常用GM/行1")]
    [Button("Gmlist")]
    public void GmList() => SetGMbarStr("/jy_cmd gmlist 4");

    [HorizontalGroup("常用GM/行1")]
    [Button("添加物品")]
    public void GmAddItem() => SetGMbarStr("/jy_gm additem:101 1 0");

    [HorizontalGroup("常用GM/行1")]
    [Button("活动进入下一阶段")]
    public void GmActiveNextState() => SetGMbarStr("/jy_gm activitynextstate:5");

    [HorizontalGroup("常用GM/行1")]
    [Button("GM获得")]
    public void GmQuickItem() => ExecuteGm("/jy_cmd quickitem");

    [HorizontalGroup("常用GM/行2")]
    [Button("清空背包")]
    public void ClearBag() => SetGMbarStrAndAutoSendCmd("/jy_gm clearbag:");

    [HorizontalGroup("常用GM/行2")]
    [Button("重置所有副本次数")]
    public void ResetDayCount() => SetGMbarStrAndAutoSendCmd("/jy_gm resetdaycount:");

    [HorizontalGroup("常用GM/行2")]
    [Button("addday")]
    public void AddDay() => SetGMbarStrAndAutoSendCmd("/jy_gm addday:");

    [TitleGroup("金币道具")]
    [HorizontalGroup("金币道具/行1")]
    [Button("加10000代币")]
    public void Add10000Token() => SetGMbarStrAndAutoSendCmd("/jy_gm additem:26042 10000 0");

    [HorizontalGroup("金币道具/行1")]
    [Button("清空货币")]
    public void ClearMoney() => SetGMbarStr("/jy_gm decmoney:9999999999");

    [HorizontalGroup("金币道具/行1")]
    [Button("加钱")]
    public void AddMoney() => SetGMbarStr("/jy_gm addmoney:9999999999");

    [HorizontalGroup("金币道具/行1")]
    [Button("充值")]
    public void AddChongzhi() => SetGMbarStr("/jy_gm addchongzhi:9999999999");




    [TitleGroup("角色升级")]
    [HorizontalGroup("角色升级/行1")]
    [Button("转职")]
    public void ZhuanZhi() => SetGMbarStr("/jy_cmd zhuanzhi 4");

    [HorizontalGroup("角色升级/行1")]
    [Button("加等级")]
    public void AddLevel() => SetGMbarStr("/jy_gm setrolelevel:300");

    [HorizontalGroup("角色升级/行1")]
    [Button("跳任务")]
    public void JumpTask() => SetGMbarStr("/jy_gm jumptotrunk:3000");

    [HorizontalGroup("角色升级/行1")]
    [Button("设置VIP")]
    public void SetVip() => SetGMbarStr("/jy_gm setvip:12");

    // [HorizontalGroup("角色升级/行2")]
    // [Button("自动转职+升级+穿戴")]
    // public void AutoUpgrade() => SetGMbarStr("/jy_gm equipbodygmoperate:102 1 5 0");

    [HorizontalGroup("角色升级/行2")]
    [Button("完成转职任务")]
    public void FinishZhuanZhiTask() => SetGMbarStr("/jy_gm GMRoleZhuanZhiOpera:100 0 0 0");

    [HorizontalGroup("角色升级/行2")]
    [Button("境界修为")]
    public void XiuWeiGM() => SetGMbarStr("/jy_gm XiuWeiGmOperate:0 6 0");

    [TitleGroup("战斗")]
    [HorizontalGroup("战斗/行1")]
    [Button("一键快速跑新手")]
    public void QuickNewbie()
    {
        string str = "/jy_gm changespeed:2500";
        SetGMbarStr(str);
        ExecuteGm(str);

        ExecuteGm("/jy_gm changegongji:9999999999");
    }

    [HorizontalGroup("战斗/行1")]
    [Button("一键血量移速攻击")]
    public void OneKeyBoost()
    {
        SetGMbarStrAndAutoSendCmd("/jy_gm changespeed:3000");
        SetGMbarStrAndAutoSendCmd("/jy_gm changemaxhp:9999999999");
        SetGMbarStrAndAutoSendCmd("/jy_gm sethp:9999999999");
    }

    [HorizontalGroup("战斗/行1")]
    [Button("重置天神CD")]
    public void ResetTianShenCD() => ExecuteGm("/jy_gm setValue:7 1 1");

    [HorizontalGroup("战斗/行1")]
    [Button("重置机甲变身CD")]
    public void ResetMechanCD() => ExecuteGm("/jy_gm MechanGmOperate:999 0 0 0");

    [HorizontalGroup("战斗/行2")]
    [Button("杀掉所有怪")]
    public void KillAllMonster() => ExecuteGm("/jy_gm killallfbmonster:");

    [HorizontalGroup("战斗/行2")]
    [Button("杀死目标怪")]
    public void KillNearMonster() => ExecuteGm("/jy_gm killnearmonsters:1");

    [HorizontalGroup("战斗/行2")]
    [Button("跨服杀掉所有怪")]
    public void CrossKillAllMonster() => ExecuteGm("/jy_crossgm killallfbmonster:");

    [HorizontalGroup("战斗/行2")]
    [Button("跨服杀死目标怪")]
    public void CrossKillNearMonster() => ExecuteGm("/jy_crossgm killnearmonsters:1");

    [HorizontalGroup("战斗/行3")]
    [Button("增加攻击力")]
    public void AddAttack() => SetGMbarStr("/jy_gm changegongji:9999999999");



    [TitleGroup("杂项")]
    [HorizontalGroup("杂项/行1")]
    [Button("设置审核服状态")]
    public void SetAuditVersion()
    {
        PlayerPrefHelper.SetInt("IS_AUDIT_VERSION", 1);
        if (EditorApplication.isPlaying)
        {
            Debug.Log("设置成功, 请重启游戏（不支持快速登录）");
        }
        else
        {
            Debug.Log("设置成功（不支持快速登录）");
        }
    }

    [HorizontalGroup("杂项/行1")]
    [Button("取消审核服状态")]
    public void CancelAuditVersion()
    {
        PlayerPrefHelper.SetInt("IS_AUDIT_VERSION", 0);
        Debug.Log("取消成功");
    }

    [HorizontalGroup("杂项/行1")]
    [Button("设置刘海屏")]
    public void SetSafeArea() => SetGMbarStrAndAutoSendCmd("/jy_cmd SetInt safe_area_mode 1");

    [HorizontalGroup("杂项/行1")]
    [Button("【慎点】清除所有PlayerPrefs缓存")]
    [GUIColor(1f, 0.3f, 0.3f)]
    public void ClearAllPlayerPrefs()
    {
        PlayerPrefs.DeleteAll();
        PlayerPrefs.Save();
    }

    [TitleGroup("调试工具")]
    [HorizontalGroup("调试工具/行1")]
    [Button("打开挂点物件面板")]
    public void OpenRolePartToolsView() => ExecuteGm("/jy_cmd role_part_tools_view");

    [HorizontalGroup("调试工具/行1")]
    [Button("测试方法重新加载")]
    public void TestLuaReload() => ExecuteGm("/jy_cmd LuaReloadTest");

    [HorizontalGroup("调试工具/行1")]
    [Button("测试断线重连")]
    public void TestDisconnect() => ExecuteGm("/jy_cmd disconnect game server");

    [HorizontalGroup("调试工具/行1")]
    [Button("战斗打印开关")]
    public void DebugFight() => ExecuteGm("/jy_cmd debugfight");

    [HorizontalGroup("调试工具/行2")]
    [Button("打印玩家属性")]
    public void ShowBaseAttr() => ExecuteGm("/jy_cmd showbaseattr");

    [HorizontalGroup("调试工具/行2")]
    [Button("开启属性监听")]
    public void EnableAttrMonitor() => ExecuteGm("/jy_cmd AttrMonitor on");

    [HorizontalGroup("调试工具/行2")]
    [Button("关闭属性监听")]
    public void DisableAttrMonitor() => ExecuteGm("/jy_cmd AttrMonitor off");

    [HorizontalGroup("调试工具/行2")]
    [Button("开启主角Buff监听")]
    public void EnableMainBuffMonitor() => ExecuteGm("/jy_cmd MainBuffMonitor on");


    [HorizontalGroup("调试工具/行3")]
    [Button("打开测试面板")]
    public void OpenTestView() => ExecuteGm("/jy_cmd view");

    [HorizontalGroup("调试工具/行3")]
    [Button("查看开服时间")]
    public void CheckOpenServerDay() => ExecuteGm("/jy_cmd openserverday");

    [HorizontalGroup("调试工具/行3")]
    [Button("代码测试1")]
    public void CodeTest1() => ExecuteGm("/jy_cmd openTest1");

    [HorizontalGroup("调试工具/行3")]
    [Button("代码测试2")]
    public void CodeTest2() => ExecuteGm("/jy_cmd openTest2");



    [HorizontalGroup("调试工具/行4")]
    [Button("打开功能界面")]
    public void OpenGmView() => SetGMbarStr("/jy_cmd gmopenview ");

    [HorizontalGroup("调试工具/行4")]
    [Button("打开内置GM界面")]
    public void OpenGmInterface() => ExecuteGm("/jy_cmd opengm");

    [HorizontalGroup("调试工具/行4")]
    [Button("截图")]
    public void TakeScreenshot() => ExecuteGm("/jy_cmd screenshot");

    [HorizontalGroup("调试工具/行4")]
    [Button("打开雾效")]
    public void EnableFog()
    {
        if (Application.isPlaying)
        {
            RenderSettings.fog = true;
            RenderSettings.fogColor = new Color(0.48f, 0.62f, 0.79f);
            RenderSettings.fogStartDistance = 20;
            RenderSettings.fogEndDistance = 150;
            RenderSettings.fogMode = FogMode.Linear;
        }
    }
    #endregion

    #region 截图功能
    [BoxGroup("截图功能")]
    [LabelText("截图路径")]
    [PropertyOrder(1000)]
    [ShowIf("@EditorApplication.isPlaying")]
    [FolderPath]
    public string screenshotPath = "";

    [BoxGroup("截图功能")]
    [LabelText("截图尺寸")]
    [ShowIf("@EditorApplication.isPlaying")]
    [Range(1, 4)]
    [PropertyOrder(1001)]
    public int screenShotSize = 1;

    [BoxGroup("截图功能")]
    [Button("截图")]
    [ShowIf("@EditorApplication.isPlaying")]
    [GUIColor(0.8f, 0.8f, 1f)]
    [PropertyOrder(1002)]
    public void TakeCustomScreenshot()
    {
        if (string.IsNullOrEmpty(screenshotPath))
            screenshotPath = UnityEngine.Application.persistentDataPath;
        
        var newPath = screenshotPath + "/" + System.DateTime.UtcNow.ToFileTime().ToString() + ".jpg";
        ScreenShot(newPath, screenShotSize);
    }
    #endregion

    #region 模型测试功能
    [BoxGroup("恭喜获得模型测试")]
    [LabelText("测试类型")]
    [PropertyOrder(1010)]
    public getviewindex giv = getviewindex.a_灵宠;

    [BoxGroup("恭喜获得模型测试")]
    [LabelText("资源ID")]
    [PropertyOrder(1011)]
    public string resources = "0";

    [PropertyOrder(1012)]
    [BoxGroup("恭喜获得模型测试")]
    [Button("恭喜获得模型测试")]
    [GUIColor(1f, 0.8f, 0.4f)]
    public void TestAppearanceModel()
    {
        var str = string.Format("/jy_cmd view appearance_get_new {0} {1}", (int)giv, resources);
        ExecuteGm(str);
    }
    #endregion

    #region 辅助方法
    protected override void OnEnable()
    {
        base.OnEnable();
        if (!init)
        {
            init = true;
            user_name = DeveloperQuickLogin.UserName;
            server_id = DeveloperQuickLogin.ServerId;
            previousUserName = user_name;
            previousServerId = server_id;
        }
        
        if (string.IsNullOrEmpty(screenshotPath))
            screenshotPath = UnityEngine.Application.persistentDataPath;
        
        // 加载GM命令历史记录
        LoadCommandHistory();
        if (gmCommandHistory.Count > 0)
        {
            gmContent = gmCommandHistory[0];
            currentHistoryIndex = 0;
        }
        else
        {
            gmContent = "";
        }
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        // 保存当前设置
        if (!string.IsNullOrEmpty(user_name) || !string.IsNullOrEmpty(server_id))
        {
            SetUserName();
        }
        
        // 保存GM命令历史记录
        SaveCommandHistory();
    }

    private (int sex, int prof) GetCharacterData(CharacterType type)
    {
        return type switch
        {
            CharacterType.随机 => (-1, 0),
            CharacterType.女刺 => (0, 2),
            CharacterType.男剑 => (1, 1),
            CharacterType.女剑 => (0, 1),
            CharacterType.男法 => (1, 3),
            CharacterType.女法 => (0, 3),
            _ => (-1, 0)
        };
    }

    private void SetUserName()
    {
        DeveloperQuickLogin.UserName = user_name;
        DeveloperQuickLogin.ServerId = server_id;
    }

    private void SetGMbarStr(string str)
    {
        gmContent = str;
    }

    private void SetGMbarStrAndAutoSendCmd(string str)
    {
        SaveToCommandHistory(str);
        gmContent = str;
        ExecuteGm(gmContent);
    }

    private void ScreenShot(string path, int size = 1)
    {
        UtilU3d.Screenshot(path, null, size);
    }

    // GM命令历史记录相关方法
    private void LoadCommandHistory()
    {
        gmCommandHistory.Clear();
        int count = EditorPrefs.GetInt("a3_fanli_history_gm_count", 0);
        for (int i = 0; i < count; i++)
        {
            string command = EditorPrefs.GetString($"a3_fanli_history_gm_{i}", "");
            if (!string.IsNullOrEmpty(command))
            {
                gmCommandHistory.Add(command);
            }
        }
        ResetHistoryNavigation();
    }

    private void SaveCommandHistory()
    {
        try
        {
            // 清除旧的历史记录
            int oldCount = EditorPrefs.GetInt("a3_fanli_history_gm_count", 0);
            for (int i = 0; i < oldCount; i++)
            {
                EditorPrefs.DeleteKey($"a3_fanli_history_gm_{i}");
            }
            
            // 保存新的历史记录
            EditorPrefs.SetInt("a3_fanli_history_gm_count", gmCommandHistory.Count);
            for (int i = 0; i < gmCommandHistory.Count; i++)
            {
                EditorPrefs.SetString($"a3_fanli_history_gm_{i}", gmCommandHistory[i]);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"保存GM命令历史记录失败: {e.Message}");
        }
    }

    private void SaveToCommandHistory(string command)
    {
        if (string.IsNullOrWhiteSpace(command))
            return;

        // 移除重复的命令
        gmCommandHistory.RemoveAll(cmd => cmd == command);
        
        gmCommandHistory.Insert(0, command);
        currentHistoryIndex = 0;
        
        // 限制历史记录数量（最多保存50条）
        const int maxHistoryCount = 50;
        if (gmCommandHistory.Count > maxHistoryCount)
        {
            gmCommandHistory.RemoveRange(maxHistoryCount, gmCommandHistory.Count - maxHistoryCount);
        }
        
        SaveCommandHistory();
    }

    private void NavigateHistory(int direction)
    {
        if (gmCommandHistory.Count == 0)
            return;

        // 如果不在导航状态，保存当前编辑的内容
        if (!isNavigatingHistory)
        {
            currentEditingCommand = gmContent;
            isNavigatingHistory = true;
        }

        int newIndex = currentHistoryIndex + direction;
        
        if (newIndex <= -1)
        {
            newIndex = gmCommandHistory.Count - 1;
        }
        else if (newIndex >= gmCommandHistory.Count)
        {
            newIndex = 0;
        }

        currentHistoryIndex = newIndex;
        gmContent = gmCommandHistory[currentHistoryIndex];
        Repaint();
    }

    private void ResetHistoryNavigation()
    {
        currentHistoryIndex = -1;
        isNavigatingHistory = false;
        currentEditingCommand = "";
    }


    #endregion
}
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class UIFollowDistance : MonoBehaviour {
    [SerializeField]
    private Transform targetTransform;
    [SerializeField]
    private float minDistance;
    [SerializeField]
    private float maxDistance;
    [SerializeField]
    private UIFollowDistanceAnimationCurve animationCurve ;
    [SerializeField]
    private float fadeTime = 0.5f;
    [SerializeField]
    private Camera overrideCamera; // 可选的相机覆盖，如果不设置则使用Camera.main

    private float sqrtMinDistance;
    private float sqrtMaxDistance;
    private float offset;
    private Vector3 lastTargetPos;
    private Vector3 lastCameraPos;
    private Canvas canvas;
    private int sortingOrderBegin;
    private Vector3 localScale = Vector3.zero;
    private float timer = -1f;
    private float fadeTimeMultiplier = 0f;
    private int updateNumber = 0;
    private Camera currentCamera; // 当前使用的相机

    private static int totalCount = 0;
    
    void Start()
    {
        this.sqrtMinDistance = this.minDistance * this.minDistance;
        this.sqrtMaxDistance = this.maxDistance * this.maxDistance;
        this.offset = this.maxDistance - this.minDistance;
        fadeTimeMultiplier = fadeTime != 0 ? 1 / fadeTime : fadeTimeMultiplier;

        canvas = this.transform.GetComponent<Canvas>();
        Canvas parent_canvas = this.transform.parent.GetComponent<Canvas>();
        if (null!= parent_canvas) sortingOrderBegin = parent_canvas.sortingOrder;

        // 初始化相机引用
        this.InitializeCamera();
    }

    void OnEnable()
    {
        ++totalCount;
        updateNumber = totalCount;
        this.lastTargetPos = Vector3.zero;
        this.lastCameraPos = Vector3.zero;
    }

    void OnDisable()
    {
        --totalCount;
        timer = -1f;
    }

    private void Update()
    {
        --updateNumber;
        if (updateNumber <= 0)
        {
            UpdateDistance();
            updateNumber = totalCount;
        }

        if (timer > 0)
        {
            timer -= Time.deltaTime;
            transform.localScale = Vector3.Slerp(transform.localScale, localScale, fadeTimeMultiplier * (fadeTime - timer));
        }
    }

    public void UpdateDistance()
    {
        // 确保相机引用有效
        if (!this.IsValidCamera(this.currentCamera))
        {
            this.InitializeCamera();
        }

        if (null != this.targetTransform && this.IsValidCamera(this.currentCamera))
        {
            // 检查目标位置是否有效
            if (!this.IsValidPosition(this.targetTransform.position))
            {
                Debug.LogWarning("UIFollowDistance: Invalid target position");
                return;
            }

            if (this.lastTargetPos != this.targetTransform.position || this.lastCameraPos != this.currentCamera.transform.position)
            {
                this.lastTargetPos = this.targetTransform.position;
                this.lastCameraPos = this.currentCamera.transform.position;
                this.UpdateScale();
            }
        }
        else
        {
            // 如果没有有效的目标或相机，重置为默认缩放
            localScale.x = 1;
            localScale.y = 1;
            localScale.z = 1;
            this.transform.localScale = localScale;
            timer = -1f;
        }
    }

    private void UpdateScale(bool imminently = false)
    {
        if (null != this.targetTransform && this.IsValidCamera(this.currentCamera))
        {
            float lastScale = this.transform.localScale.x;

            var sqrtDistance = (this.targetTransform.position - this.currentCamera.transform.position).sqrMagnitude;

            // 检查距离是否有效
            if (float.IsNaN(sqrtDistance) || float.IsInfinity(sqrtDistance))
            {
                Debug.LogWarning("UIFollowDistance: Invalid distance calculated");
                return;
            }

            if (!this.IsVisible() || sqrtDistance > sqrtMaxDistance)
            {
                localScale.x = 0;
                localScale.y = 0;
                localScale.z = 0;
                this.transform.localScale = localScale;
                timer = -1;
            }
            else if (sqrtDistance <= this.sqrtMinDistance)
            {
                localScale.x = 1;
                localScale.y = 1;
                localScale.z = 1;
                this.transform.localScale = localScale;
                timer = -1f;
            }
            else
            {
                var distance = Mathf.Sqrt(sqrtDistance) - this.minDistance;
                var scale = Mathf.Clamp(animationCurve.Curve.Evaluate(distance / this.offset), 0f, 1f);
                if (localScale.x == 0)
                {
                    imminently = true;
                }
                localScale.x = scale;
                localScale.y = scale;
                localScale.z = scale;
                if (imminently)
                {
                    this.transform.localScale = localScale;
                    timer = -1f;
                }
                else
                {
                    if (timer <= 0)
                    {
                        timer = fadeTime;
                    }
                }
            }

            if (null != canvas && Mathf.Abs(this.transform.localScale.x - lastScale) >= 0.001f)
            {
                canvas.overrideSorting = true;
                canvas.sortingOrder = sortingOrderBegin + (int)(this.transform.localScale.x * 1000.0f);
            }
        }
    }

    // 是否在视野内
    private bool IsVisible()
    {
        if (null == this.targetTransform || !this.IsValidCamera(this.currentCamera))
        {
            return false;
        }

        // 修复可见性检测逻辑
        Vector3 directionToTarget = this.targetTransform.position - this.currentCamera.transform.position;

        // 检查目标是否在相机前方
        float dotProduct = Vector3.Dot(directionToTarget.normalized, this.currentCamera.transform.forward);

        // 如果点积大于0，说明目标在相机前方
        bool inFront = dotProduct > 0;

        if (!inFront)
        {
            return false;
        }

        // 可选：检查是否在相机视锥体内
        Vector3 viewportPoint = this.currentCamera.WorldToViewportPoint(this.targetTransform.position);
        bool inViewport = viewportPoint.x >= 0 && viewportPoint.x <= 1 &&
                         viewportPoint.y >= 0 && viewportPoint.y <= 1 &&
                         viewportPoint.z > 0;

        return inViewport;
    }

    public float MinDistance
    {
        set
        {
            if (this.minDistance != value)
            {
                this.minDistance = value;
                this.sqrtMinDistance = this.minDistance * this.minDistance;
                this.offset = this.maxDistance - this.minDistance;
                this.UpdateScale();
            }
        }
        get { return this.minDistance; }
    }

    public float MaxDistance
    {
        set
        {
            if (this.maxDistance != value)
            {
                this.maxDistance = value;
                this.sqrtMaxDistance = this.maxDistance * this.maxDistance;
                this.offset = this.maxDistance - this.minDistance;
                this.UpdateScale();
            }
        }
        get { return this.maxDistance; }
    }

    public Transform TargetTransform
    {
        set
        {
            this.targetTransform = value;
            if (null != this.targetTransform)
            {
                this.lastTargetPos = this.targetTransform.position;
                this.UpdateScale(true);
            }
        }
        get { return this.targetTransform; }
    }

    /// <summary>
    /// 获取或设置覆盖相机
    /// </summary>
    public Camera OverrideCamera
    {
        get { return this.overrideCamera; }
        set
        {
            this.overrideCamera = value;
            this.InitializeCamera();
        }
    }

    /// <summary>
    /// 初始化相机引用
    /// </summary>
    private void InitializeCamera()
    {
        // 优先使用覆盖相机
        if (this.IsValidCamera(this.overrideCamera))
        {
            this.currentCamera = this.overrideCamera;
            return;
        }

        // 其次使用Camera.main
        if (this.IsValidCamera(Camera.main))
        {
            this.currentCamera = Camera.main;
            return;
        }

        Debug.LogError("UIFollowDistance: No valid camera found");
        this.currentCamera = null;
    }

    /// <summary>
    /// 检查相机是否有效
    /// </summary>
    private bool IsValidCamera(Camera camera)
    {
        return camera != null && camera.enabled && camera.gameObject.activeInHierarchy;
    }

    /// <summary>
    /// 检查位置是否有效
    /// </summary>
    private bool IsValidPosition(Vector3 position)
    {
        return !float.IsNaN(position.x) && !float.IsNaN(position.y) && !float.IsNaN(position.z) &&
               !float.IsInfinity(position.x) && !float.IsInfinity(position.y) && !float.IsInfinity(position.z);
    }
}

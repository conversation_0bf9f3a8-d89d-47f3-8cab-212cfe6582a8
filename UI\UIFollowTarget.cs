using UnityEngine;
using UnityEngine.Assertions;

/// <summary>
/// The UI follow the specify target.
/// </summary>
[RequireComponent(typeof(RectTransform))]
public sealed class UIFollowTarget : MonoBehaviour
{
    [SerializeField]
    private Transform target;

    [SerializeField]
    private Camera gameCamera;

    [SerializeField]
    private Canvas canvas;

    // Dependency components.
    private RectTransform rectTransform;

    /// <summary>
    /// Gets or sets the 3D target that this object will be positioned
    /// above.
    /// </summary>
    public Transform Target
    {
        get { return this.target; }
        set { this.target = value; }
    }

    /// <summary>
    /// Gets or sets the UI canvas.
    /// </summary>
    public Canvas Canvas
    {
        get { return this.canvas; }
        set { this.canvas = value; }
    }

    /// <summary>
    /// Gets or sets the camera.
    /// </summary>
    public Camera Camera
    {
        get { return this.gameCamera; }
        set { this.gameCamera = value; }
    }

    /// <summary>
    /// Calculate the screen position.
    /// </summary>
    public static Vector3 CalculateScreenPosition(
        Vector3 position,
        Camera camera,
        Canvas canvas,
        RectTransform transform)
    {
        Assert.IsNotNull(camera);
        Assert.IsNotNull(canvas);
        Assert.IsNotNull(transform);

        var screenPos = camera.WorldToScreenPoint(position);

        // 检查屏幕坐标是否有效
        if (float.IsNaN(screenPos.x) || float.IsNaN(screenPos.y) || float.IsNaN(screenPos.z))
        {
            Debug.LogWarning("UIFollowTarget: Invalid screen position calculated");
            return Vector3.zero;
        }

        var pos = Vector3.zero;
        bool success = false;

        switch (canvas.renderMode)
        {
            case RenderMode.ScreenSpaceOverlay:
                success = RectTransformUtility.ScreenPointToWorldPointInRectangle(
                    transform,
                    screenPos,
                    null,
                    out pos);
                break;
            case RenderMode.ScreenSpaceCamera:
            case RenderMode.WorldSpace:
                // 确保worldCamera不为null
                Camera uiCamera = canvas.worldCamera;
                if (uiCamera == null)
                {
                    uiCamera = Camera.main;
                }

                if (uiCamera != null)
                {
                    success = RectTransformUtility.ScreenPointToWorldPointInRectangle(
                        transform,
                        screenPos,
                        uiCamera,
                        out pos);
                }
                else
                {
                    Debug.LogError("UIFollowTarget: No valid camera found for UI positioning");
                    return Vector3.zero;
                }
                break;
        }

        if (!success)
        {
            return Vector3.zero;
        }

        return pos;
    }

    private void Awake()
    {
        this.rectTransform = this.GetComponent<RectTransform>();
        this.InitializeCamera();
    }

    private void Start()
    {
        this.SyncPosition();
    }

    private void LateUpdate()
    {
        // 确保相机引用有效
        if (!this.IsValidCamera(this.gameCamera))
        {
            this.InitializeCamera();
        }

        this.SyncPosition();
    }

    /// <summary>
    /// 初始化相机引用
    /// </summary>
    private void InitializeCamera()
    {
        if (this.gameCamera == null)
        {
            this.gameCamera = Camera.main;
        }
    }

    /// <summary>
    /// 检查相机是否有效
    /// </summary>
    private bool IsValidCamera(Camera camera)
    {
        return camera != null && camera.enabled && camera.gameObject.activeInHierarchy;
    }

    private void SyncPosition()
    {
        if (this.target == null ||
            this.canvas == null ||
            this.gameCamera == null ||
            this.rectTransform == null)
        {
            return;
        }

        // 检查目标位置是否有效
        if (!this.IsValidPosition(this.target.position))
        {
            return;
        }

        var pos = CalculateScreenPosition(
            this.target.position,
            this.gameCamera,
            this.canvas,
            this.rectTransform);

        this.transform.position = pos;
    }

    /// <summary>
    /// 检查3D位置是否有效
    /// </summary>
    private bool IsValidPosition(Vector3 position)
    {
        return !float.IsNaN(position.x) && !float.IsNaN(position.y) && !float.IsNaN(position.z) &&
               !float.IsInfinity(position.x) && !float.IsInfinity(position.y) && !float.IsInfinity(position.z);
    }
}
